<!DOCTYPE html>
<html>
<head>
    <title>SignalR Status Monitoring Test</title>
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .update { background-color: #f8f9fa; border-left: 4px solid #007bff; padding: 10px; margin: 5px 0; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; }
        #updates { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
    </style>
</head>
<body>
    <h1>SignalR Status Monitoring Test</h1>
    
    <div id="status" class="status disconnected">Not connected</div>
    
    <div>
        <input type="text" id="teamId" placeholder="Enter Team Leader ID" value="test-teamleader-123" />
        <button onclick="joinTeam()">Join Team Leader Group</button>
        <button onclick="leaveTeam()">Leave Team Leader Group</button>
    </div>
    
    <div>
        <input type="text" id="shiftId" placeholder="Enter Shift Instant ID" value="test-shift-456" />
        <button onclick="joinShift()">Join Shift Group</button>
        <button onclick="leaveShift()">Leave Shift Group</button>
    </div>
    
    <button onclick="clearUpdates()">Clear Updates</button>
    
    <h3>Real-time Updates:</h3>
    <div id="updates"></div>

    <script>
        let connection;

        // Initialize SignalR connection
        function initializeConnection() {
            // Use the correct URL for your service
            const hubUrl = "http://localhost:8082/hub/status-monitoring";
            
            connection = new signalR.HubConnectionBuilder()
                .withUrl(hubUrl)
                .withAutomaticReconnect()
                .configureLogging(signalR.LogLevel.Information)
                .build();

            // Connection event handlers
            connection.start().then(function () {
                console.log("Connected to SignalR hub");
                updateStatus("Connected to SignalR hub", true);
                addUpdate("✅ Connected to SignalR hub");
            }).catch(function (err) {
                console.error("Error connecting to SignalR hub:", err);
                updateStatus("Connection failed: " + err.message, false);
                addUpdate("❌ Connection failed: " + err.message);
            });

            connection.onreconnecting(function () {
                console.log("Reconnecting to SignalR hub...");
                updateStatus("Reconnecting...", false);
                addUpdate("🔄 Reconnecting...");
            });

            connection.onreconnected(function () {
                console.log("Reconnected to SignalR hub");
                updateStatus("Reconnected to SignalR hub", true);
                addUpdate("✅ Reconnected to SignalR hub");
            });

            connection.onclose(function () {
                console.log("Disconnected from SignalR hub");
                updateStatus("Disconnected from SignalR hub", false);
                addUpdate("❌ Disconnected from SignalR hub");
            });

            // Listen for group join/leave confirmations
            connection.on("JoinedTeamGroup", function (teamId) {
                console.log(`Joined team leader group: ${teamId}`);
                addUpdate(`✅ Joined team leader group: ${teamId}`);
            });

            connection.on("LeftTeamGroup", function (teamId) {
                console.log(`Left team leader group: ${teamId}`);
                addUpdate(`👋 Left team leader group: ${teamId}`);
            });

            connection.on("JoinedShiftInstantGroup", function (shiftInstantId) {
                console.log(`Joined shift instant group: ${shiftInstantId}`);
                addUpdate(`✅ Joined shift instant group: ${shiftInstantId}`);
            });

            connection.on("LeftShiftInstantGroup", function (shiftInstantId) {
                console.log(`Left shift instant group: ${shiftInstantId}`);
                addUpdate(`👋 Left shift instant group: ${shiftInstantId}`);
            });

            // Listen for status updates
            connection.on("OperatorStatusUpdate", function (data) {
                console.log("Operator status update:", data);
                
                // Check if data is defined
                if (!data) {
                    console.error("OperatorStatusUpdate: data is undefined");
                    addUpdate("❌ Received operator status update but data is undefined");
                    return;
                }
                
                // Safely extract properties with defaults
                const operatorId = data.operatorId || 'Unknown';
                const teamId = data.teamId || 'Unknown';
                const statusCode = data.statusCode || 'Unknown';
                const timestamp = data.timestamp || new Date();

                try {
                    addUpdate(`👤 Operator ${operatorId} in team leader ${teamId} status changed to ${statusCode} at ${new Date(timestamp).toLocaleTimeString()}`);
                } catch (error) {
                    console.error("Error processing operator status timestamp:", error);
                    addUpdate(`👤 Operator ${operatorId} in team leader ${teamId} status changed to ${statusCode} (timestamp error)`);
                }
            });

            connection.on("ShiftInstantStatusUpdate",  function (data) {
                try {
                console.log("Shift status update:", data);
                
                // Check if data is defined
                if (!data) {
                    console.error("ShiftInstantStatusUpdate: data is undefined");
                    addUpdate("❌ Received shift status update but data is undefined");
                    return;
                }
                
                        // Add a small delay to ensure data is fully populated
                // await new Promise(resolve => setTimeout(resolve, 10));
                

                // Debug: log all timestamp properties
                console.log("Available timestamp properties:", {
                    timestamp: data.timestamp,
                    updatedAt: data.updatedAt
                });
                
                // Safely extract properties with defaults
                const shiftId = data.shiftInstantId || 'Unknown';
                const previousStatus = data.previousStatus || 'Unknown';
                const currentStatus = data.currentStatus || 'Unknown';
                
                // Use UpdatedAt instead of Timestamp, and add fallback
                const timestamp = data.UpdatedAt || data.Timestamp || data.updatedAt || data.timestamp || new Date();
                
                const updateMessage = `Shift ${shiftId} status changed from ${previousStatus} to ${currentStatus} at ${new Date(timestamp).toLocaleTimeString()}`
                console.log("Shift status update:", updateMessage);
                addUpdate(updateMessage);
            } catch (error) {
                console.error("Error processing timestamp:", error);
                addUpdate(`🔄 Shift ${shiftId} status changed from ${previousStatus} to ${currentStatus} (timestamp error)`);
            }
            });

            connection.on("ShiftViewUpdate", function (data) {
                try {
                console.log("Shift view update:", data);
                
                // Check if data is defined
                if (!data) {
                    console.error("ShiftViewUpdate: data is undefined");
                    addUpdate("❌ Received shift view update but data is undefined");
                    return;
                }
                
                // Safely extract properties with defaults
                const teamId = data.teamId || 'Unknown';
                const shiftDate = data.shiftDate || 'Unknown';
                const updatedAt = data.updatedAt || new Date();
                
                    addUpdate(`📊 Shift view updated for team leader ${teamId} on ${shiftDate} at ${new Date(updatedAt).toLocaleTimeString()}`);
                } catch (error) {
                    console.error("Error processing shift view timestamp:", error);
                    addUpdate(`📊 Shift view updated for team leader ${teamId} on ${shiftDate} (timestamp error)`);
                }
            });

            connection.on("TeamStatusMonitoringUpdate", function (data) {
                try {
                console.log("Team status monitoring update:", data);
                
                // Check if data is defined
                if (!data) {
                    console.error("TeamStatusMonitoringUpdate: data is undefined");
                    addUpdate("❌ Received team status monitoring update but data is undefined");
                    return;
                }
                
                // Safely extract properties with defaults
                const teamId = data.teamId || 'Unknown';
                const updatedAt = data.updatedAt || new Date();

                
                    addUpdate(`📈 Team Leader ${teamId} status monitoring data refreshed at ${new Date(updatedAt).toLocaleTimeString()}`);
                } catch (error) {
                    console.error("Error processing team monitoring timestamp:", error);
                    addUpdate(`📈 Team Leader ${teamId} status monitoring data refreshed (timestamp error)`);
                }
            });
        }

        function updateStatus(message, isConnected) {
            const statusElement = document.getElementById("status");
            statusElement.textContent = message;
            statusElement.className = "status " + (isConnected ? "connected" : "disconnected");
        }
        function addUpdate(message) {
            const div = document.createElement("div");
            div.className = "update";
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            const updatesContainer = document.getElementById("updates");
            updatesContainer.appendChild(div);
            updatesContainer.scrollTop = updatesContainer.scrollHeight;
        }

        function joinTeam() {
            const teamId = document.getElementById("teamId").value;
            if (teamId && connection) {
                connection.invoke("JoinTeamGroup", teamId).catch(function (err) {
                    console.error("Error joining team leader group:", err);
                    addUpdate(`❌ Error joining team leader group: ${err.message}`);
                });
            } else {
                addUpdate("❌ Please enter a team leader ID and ensure connection is established");
            }
        }

        function leaveTeam() {
            const teamId = document.getElementById("teamId").value;
            if (teamId && connection) {
                connection.invoke("LeaveTeamGroup", teamId).catch(function (err) {
                    console.error("Error leaving team leader group:", err);
                    addUpdate(`❌ Error leaving team leader group: ${err.message}`);
                });
            } else {
                addUpdate("❌ Please enter a team leader ID and ensure connection is established");
            }
        }

        function joinShift() {
            const shiftId = document.getElementById("shiftId").value;
            if (shiftId && connection) {
                connection.invoke("JoinShiftInstantGroup", shiftId).catch(function (err) {
                    console.error("Error joining shift group:", err);
                    addUpdate(`❌ Error joining shift group: ${err.message}`);
                });
            } else {
                addUpdate("❌ Please enter a shift ID and ensure connection is established");
            }
        }

        function leaveShift() {
            const shiftId = document.getElementById("shiftId").value;
            if (shiftId && connection) {
                connection.invoke("LeaveShiftInstantGroup", shiftId).catch(function (err) {
                    console.error("Error leaving shift group:", err);
                    addUpdate(`❌ Error leaving shift group: ${err.message}`);
                });
            } else {
                addUpdate("❌ Please enter a shift ID and ensure connection is established");
            }
        }

        function clearUpdates() {
            document.getElementById("updates").innerHTML = "";
        }

        // Initialize connection when page loads
        initializeConnection();
    </script>
</body>
</html>
